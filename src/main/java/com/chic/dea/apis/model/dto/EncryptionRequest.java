package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 加密请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class EncryptionRequest {

    /**
     * 单个明文（单个加密时使用）
     */
    @NotBlank(message = "明文不能为空", groups = {SingleEncryption.class})
    private String plainText;

    /**
     * 多个明文（批量加密时使用）
     */
    @NotEmpty(message = "明文列表不能为空", groups = {BatchEncryption.class})
    private List<String> plainTexts;

    /**
     * 单个加密验证组
     */
    public interface SingleEncryption {}

    /**
     * 批量加密验证组
     */
    public interface BatchEncryption {}
}
